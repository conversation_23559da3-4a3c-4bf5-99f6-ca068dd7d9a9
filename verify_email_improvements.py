#!/usr/bin/env python3
"""
Verify the email template improvements without Flask context
"""

import os

def check_template_file(file_path, checks):
    """Check a template file for specific content"""
    if not os.path.exists(file_path):
        print(f"❌ Template file not found: {file_path}")
        return False
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print(f"✅ Template file loaded: {file_path}")
        print(f"   📏 Size: {len(content):,} characters")
        
        results = []
        for check_name, search_term in checks:
            found = search_term in content
            status = "✅" if found else "❌"
            print(f"   {status} {check_name}")
            results.append(found)
        
        return all(results)
        
    except Exception as e:
        print(f"❌ Error reading template: {e}")
        return False

def verify_base_template():
    """Verify the base email template improvements"""
    print("🧪 Verifying Base Email Template")
    print("-" * 40)
    
    checks = [
        ("CVBioLabs logo image", "CV.png"),
        ("Logo fallback", "logo-fallback"),
        ("Company name", "CVBioLabs"),
        ("Enhanced tagline", "For a Healthy Life"),
        ("Professional header", "logo-container"),
        ("Gradient background", "linear-gradient"),
        ("Responsive design", "@media"),
        ("Brand colors", "#f47c20"),
        ("Footer contact", "<EMAIL>")
    ]
    
    return check_template_file("templates/emails/base.html", checks)

def verify_password_reset_template():
    """Verify the password reset template improvements"""
    print("\n🧪 Verifying Password Reset Template")
    print("-" * 45)
    
    checks = [
        ("Request summary section", "Request Summary"),
        ("Enhanced OTP display", "Secure Password Reset Code"),
        ("Gradient OTP box", "linear-gradient"),
        ("Professional greeting", "Dear"),
        ("Enhanced button", "Reset My Password"),
        ("Security warnings", "Important Security Information"),
        ("Support section", "Need Help?"),
        ("Professional closing", "CVBioLabs Security Team"),
        ("Trust indicators", "ISO Certified"),
        ("Request ID", "request_id"),
        ("Timestamp", "current_datetime")
    ]
    
    return check_template_file("templates/emails/password_reset.html", checks)

def verify_email_service():
    """Verify the email service improvements"""
    print("\n🧪 Verifying Email Service")
    print("-" * 30)
    
    if not os.path.exists("email_service.py"):
        print("❌ Email service file not found")
        return False
    
    try:
        with open("email_service.py", 'r', encoding='utf-8') as f:
            content = f.read()
        
        checks = [
            ("Enhanced subject line", "🔐 CVBioLabs Password Reset Request"),
            ("Request ID generation", "request_id"),
            ("Timestamp formatting", "current_datetime"),
            ("Professional context", "current_timestamp")
        ]
        
        results = []
        for check_name, search_term in checks:
            found = search_term in content
            status = "✅" if found else "❌"
            print(f"   {status} {check_name}")
            results.append(found)
        
        return all(results)
        
    except Exception as e:
        print(f"❌ Error reading email service: {e}")
        return False

def verify_app_improvements():
    """Verify the app.py improvements"""
    print("\n🧪 Verifying App Improvements")
    print("-" * 32)
    
    if not os.path.exists("app.py"):
        print("❌ App file not found")
        return False
    
    try:
        with open("app.py", 'r', encoding='utf-8') as f:
            content = f.read()
        
        checks = [
            ("Enhanced user name retrieval", "first_name"),
            ("Dictionary cursor", "dictionary=True"),
            ("User profile join", "user_profiles up"),
            ("Professional name handling", "user_name")
        ]
        
        results = []
        for check_name, search_term in checks:
            found = search_term in content
            status = "✅" if found else "❌"
            print(f"   {status} {check_name}")
            results.append(found)
        
        return all(results)
        
    except Exception as e:
        print(f"❌ Error reading app file: {e}")
        return False

def show_summary():
    """Show summary of improvements"""
    print("\n🎨 Email Template Enhancements Summary")
    print("=" * 45)
    
    improvements = [
        "🖼️  Added actual CVBioLabs logo with fallback",
        "🎨 Enhanced professional branding and colors",
        "🔐 Improved OTP display with security styling",
        "📋 Added request summary with ID and timestamp",
        "⚠️  Enhanced security warnings and tips",
        "📞 Professional support contact section",
        "🏆 Added trust indicators (ISO, NABL)",
        "🔘 Improved button styling with gradients",
        "📱 Better responsive design for mobile",
        "🎯 Enhanced call-to-action elements",
        "👤 Better user name handling from database",
        "📧 Professional email subject line with emoji"
    ]
    
    for improvement in improvements:
        print(f"  {improvement}")

def main():
    """Main verification function"""
    print("🏥 CVBioLabs Email Template Verification")
    print("=" * 45)
    
    # Show improvements
    show_summary()
    
    # Run verifications
    base_ok = verify_base_template()
    reset_ok = verify_password_reset_template()
    service_ok = verify_email_service()
    app_ok = verify_app_improvements()
    
    # Summary
    print("\n" + "=" * 45)
    print("📊 VERIFICATION SUMMARY")
    print("=" * 45)
    
    tests = [
        ("Base Template", base_ok),
        ("Password Reset Template", reset_ok),
        ("Email Service", service_ok),
        ("App Improvements", app_ok)
    ]
    
    passed = sum(1 for _, result in tests if result)
    total = len(tests)
    
    for test_name, result in tests:
        status = "✅" if result else "❌"
        print(f"{status} {test_name}")
    
    print(f"\nResults: {passed}/{total} verifications passed")
    
    if passed == total:
        print("\n🎉 All verifications passed!")
        print("✅ Professional email template is ready")
        print("🌟 CVBioLabs branding successfully applied")
        print("\n💡 To test the email:")
        print("   1. Start your Flask app: python start_app.py")
        print("   2. Go to forgot password page")
        print("   3. Enter your email to receive the enhanced email")
    else:
        print(f"\n⚠️  {total - passed} verification(s) failed")
        print("💡 Please check the issues above")
    
    return passed == total

if __name__ == '__main__':
    success = main()
    exit(0 if success else 1)
