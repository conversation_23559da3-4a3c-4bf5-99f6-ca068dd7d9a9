#!/usr/bin/env python3
"""
Test script to verify all fixes are working correctly
"""

import os
import sys
import time
import requests
from dotenv import load_dotenv

load_dotenv()

def test_application_startup():
    """Test if the application can start without errors"""
    print("🧪 Testing Application Startup")
    print("-" * 40)
    
    try:
        # Try to import the app to check for syntax errors
        sys.path.insert(0, os.getcwd())
        
        print("   ✓ Checking imports...")
        import app
        print("   ✅ All imports successful")
        
        print("   ✓ Checking Flask app creation...")
        flask_app = app.app
        print("   ✅ Flask app created successfully")
        
        print("   ✓ Checking CSRF configuration...")
        csrf_config = flask_app.config.get('WTF_CSRF_ENABLED')
        print(f"   ✅ CSRF enabled: {csrf_config}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False

def test_csrf_token_function():
    """Test CSRF token generation"""
    print("\n🔐 Testing CSRF Token Generation")
    print("-" * 40)
    
    try:
        from flask_wtf.csrf import generate_csrf
        from app import app
        
        with app.app_context():
            token = generate_csrf()
            print(f"   ✅ CSRF token generated: {token[:20]}...")
            return True
            
    except Exception as e:
        print(f"   ❌ CSRF token generation failed: {e}")
        return False

def test_template_syntax():
    """Test template syntax for CSRF tokens"""
    print("\n📄 Testing Template Syntax")
    print("-" * 40)
    
    templates_to_check = [
        'templates/reset_password.html',
        'templates/forgot_password.html',
        'templates/verify_otp.html',
        'templates/login_signup.html'
    ]
    
    all_good = True
    
    for template_path in templates_to_check:
        if os.path.exists(template_path):
            try:
                with open(template_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Check for incorrect csrf_token() usage
                if 'csrf_token()' in content:
                    print(f"   ❌ {template_path}: Found csrf_token() (should be csrf_token)")
                    all_good = False
                elif 'csrf_token' in content:
                    print(f"   ✅ {template_path}: CSRF token syntax correct")
                else:
                    print(f"   ⚠️  {template_path}: No CSRF token found")
                    
            except Exception as e:
                print(f"   ❌ Error reading {template_path}: {e}")
                all_good = False
        else:
            print(f"   ⚠️  {template_path}: File not found")
    
    return all_good

def test_rate_limiting_config():
    """Test rate limiting configuration"""
    print("\n⏱️  Testing Rate Limiting Configuration")
    print("-" * 40)
    
    try:
        from rate_limit_config import rate_config
        
        print(f"   ✓ Environment: {rate_config.env}")
        print(f"   ✓ Login limits: {rate_config.login_limits}")
        print(f"   ✓ Signup limits: {rate_config.signup_limits}")
        print(f"   ✓ Password reset limits: {rate_config.password_reset_limits}")
        print("   ✅ Rate limiting configuration loaded successfully")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Rate limiting configuration error: {e}")
        return False

def test_health_endpoint():
    """Test if health endpoint is accessible when app is running"""
    print("\n🏥 Testing Health Endpoint")
    print("-" * 40)
    
    try:
        response = requests.get('http://localhost:7000/health', timeout=5)
        
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Health endpoint accessible")
            print(f"   ✓ Status: {data.get('status')}")
            print(f"   ✓ Database: {data.get('components', {}).get('database')}")
            return True
        else:
            print(f"   ⚠️  Health endpoint returned {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("   ℹ️  Application not running (this is expected if you haven't started it)")
        return None
    except Exception as e:
        print(f"   ❌ Health endpoint test failed: {e}")
        return False

def test_duplicate_functions():
    """Check for duplicate function definitions"""
    print("\n🔍 Checking for Duplicate Functions")
    print("-" * 40)
    
    try:
        with open('app.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for duplicate inject_csrf_token functions
        csrf_count = content.count('def inject_csrf_token():')
        
        if csrf_count == 1:
            print("   ✅ Single inject_csrf_token function found")
            return True
        elif csrf_count > 1:
            print(f"   ❌ Found {csrf_count} inject_csrf_token functions (should be 1)")
            return False
        else:
            print("   ❌ No inject_csrf_token function found")
            return False
            
    except Exception as e:
        print(f"   ❌ Error checking for duplicates: {e}")
        return False

def main():
    """Run all tests"""
    print("🧪 CVBioLabs Fix Verification Tests")
    print("=" * 50)
    
    tests = [
        ("Application Startup", test_application_startup),
        ("CSRF Token Generation", test_csrf_token_function),
        ("Template Syntax", test_template_syntax),
        ("Rate Limiting Config", test_rate_limiting_config),
        ("Duplicate Functions", test_duplicate_functions),
        ("Health Endpoint", test_health_endpoint),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"\n❌ Test '{test_name}' crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 TEST SUMMARY")
    print("=" * 50)
    
    passed = 0
    failed = 0
    skipped = 0
    
    for test_name, result in results:
        if result is True:
            print(f"✅ {test_name}: PASSED")
            passed += 1
        elif result is False:
            print(f"❌ {test_name}: FAILED")
            failed += 1
        else:
            print(f"⚠️  {test_name}: SKIPPED")
            skipped += 1
    
    print(f"\nResults: {passed} passed, {failed} failed, {skipped} skipped")
    
    if failed == 0:
        print("\n🎉 All critical tests passed!")
        print("💡 Your application should now work correctly.")
        print("🚀 Start your Flask app with: python app.py")
    else:
        print(f"\n⚠️  {failed} test(s) failed. Please review the issues above.")
    
    return failed == 0

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
