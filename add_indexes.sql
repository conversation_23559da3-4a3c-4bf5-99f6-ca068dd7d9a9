-- Simple database optimization script for CVBioLabs
-- Add indexes to improve test search performance

USE cvbiolabs;

-- Drop existing indexes if they exist (ignore errors)
DROP INDEX IF EXISTS idx_testdetails_active ON testdetails;
DROP INDEX IF EXISTS idx_testdetails_search ON testdetails;
DROP INDEX IF EXISTS idx_testdetails_department ON testdetails;
DROP INDEX IF EXISTS idx_testdetails_code ON testdetails;
DROP INDEX IF EXISTS idx_testdetails_name_order ON testdetails;
DROP INDEX IF EXISTS idx_testdetails_pagination ON testdetails;

-- Create new indexes
-- Index for active tests (most common filter)
ALTER TABLE testdetails ADD INDEX idx_testdetails_active (active);

-- Index for department filtering
ALTER TABLE testdetails ADD INDEX idx_testdetails_department (DepartmentName(50), active);

-- Index for test code searches
ALTER TABLE testdetails ADD INDEX idx_testdetails_code (TestCode(50), active);

-- Index for ordering by <PERSON><PERSON><PERSON> with active filter
ALTER TABLE testdetails ADD INDEX idx_testdetails_name_order (active, TestName(50));

-- Composite index for pagination queries
ALTER TABLE testdetails ADD INDEX idx_testdetails_pagination (active, TestName(50), SrNo);

-- Show the indexes we just created
SHOW INDEX FROM testdetails WHERE Key_name LIKE 'idx_testdetails%';

-- Analyze table to update statistics
ANALYZE TABLE testdetails;

-- Show table information
SELECT 
    TABLE_NAME,
    TABLE_ROWS,
    ROUND(((DATA_LENGTH + INDEX_LENGTH) / 1024 / 1024), 2) AS 'Size_MB',
    ROUND((INDEX_LENGTH / 1024 / 1024), 2) AS 'Index_Size_MB'
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = 'cvbiolabs' AND TABLE_NAME = 'testdetails';
