#!/usr/bin/env python3
"""
Test script to preview the enhanced password reset email template
"""

import os
import sys
from datetime import datetime
import time

def test_email_template():
    """Test the enhanced email template"""
    print("🧪 Testing Enhanced Password Reset Email Template")
    print("=" * 55)
    
    try:
        # Add current directory to path
        sys.path.insert(0, os.getcwd())
        
        # Import Flask app
        from app import app
        
        with app.app_context():
            from flask import render_template
            
            # Test data
            test_context = {
                'user_name': '<PERSON>',
                'otp': '123456',
                'reset_url': 'http://localhost:7000/reset_password',
                'recipient_email': '<EMAIL>',
                'current_datetime': datetime.now().strftime('%B %d, %Y at %I:%M %p'),
                'current_timestamp': str(int(time.time())),
                'request_id': f"PWD-{int(time.time())}-1234",
                'website_url': 'http://localhost:7000',
                'current_year': datetime.now().year
            }
            
            print("✅ Flask app context created")
            print("✅ Test context prepared")
            
            # Render the template
            try:
                html_content = render_template('emails/password_reset.html', **test_context)
                print("✅ Password reset template rendered successfully")
                
                # Save to file for preview
                output_file = 'password_reset_preview.html'
                with open(output_file, 'w', encoding='utf-8') as f:
                    f.write(html_content)
                
                print(f"✅ Template saved to: {output_file}")
                print(f"🌐 Open {output_file} in your browser to preview")
                
                # Show template stats
                print(f"\n📊 Template Statistics:")
                print(f"   • Length: {len(html_content):,} characters")
                print(f"   • Lines: {html_content.count(chr(10)):,}")
                print(f"   • Contains logo: {'CV.png' in html_content}")
                print(f"   • Contains OTP: {test_context['otp'] in html_content}")
                print(f"   • Contains user name: {test_context['user_name'] in html_content}")
                
                return True
                
            except Exception as e:
                print(f"❌ Template rendering failed: {e}")
                return False
                
    except Exception as e:
        print(f"❌ Test setup failed: {e}")
        return False

def test_base_template():
    """Test the base email template"""
    print("\n🧪 Testing Base Email Template")
    print("-" * 35)
    
    try:
        from app import app
        
        with app.app_context():
            from flask import render_template
            
            test_context = {
                'website_url': 'http://localhost:7000',
                'recipient_email': '<EMAIL>',
                'current_year': datetime.now().year
            }
            
            # Test base template
            html_content = render_template('emails/base.html', **test_context)
            print("✅ Base template rendered successfully")
            
            # Check for key elements
            checks = [
                ('Logo image', 'CV.png' in html_content),
                ('Company name', 'CVBioLabs' in html_content),
                ('Tagline', 'For a Healthy Life' in html_content),
                ('Footer', '<EMAIL>' in html_content),
                ('Responsive design', '@media' in html_content)
            ]
            
            print("\n📋 Template Elements Check:")
            for check_name, passed in checks:
                status = "✅" if passed else "❌"
                print(f"   {status} {check_name}")
            
            return all(check[1] for check in checks)
            
    except Exception as e:
        print(f"❌ Base template test failed: {e}")
        return False

def show_improvements():
    """Show the improvements made to the email template"""
    print("\n🎨 Email Template Improvements")
    print("=" * 40)
    
    improvements = [
        "✅ Added actual CVBioLabs logo image",
        "✅ Enhanced header with professional branding",
        "✅ Improved OTP display with gradient background",
        "✅ Added request summary with timestamp and ID",
        "✅ Enhanced security warnings and tips",
        "✅ Professional support contact section",
        "✅ Added trust indicators (ISO, NABL certified)",
        "✅ Improved button styling with gradients",
        "✅ Better responsive design for mobile",
        "✅ Enhanced color scheme matching CVBioLabs brand",
        "✅ Added fallback for logo if image fails to load",
        "✅ Professional closing with security team signature"
    ]
    
    for improvement in improvements:
        print(f"  {improvement}")
    
    print(f"\n🎯 Key Features:")
    print(f"  • Professional CVBioLabs branding")
    print(f"  • Enhanced security and trust elements")
    print(f"  • Mobile-responsive design")
    print(f"  • Clear call-to-action buttons")
    print(f"  • Comprehensive support information")

def main():
    """Main test function"""
    print("🏥 CVBioLabs Email Template Enhancement Test")
    print("=" * 50)
    
    # Show improvements first
    show_improvements()
    
    # Test templates
    base_test = test_base_template()
    email_test = test_email_template()
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 TEST SUMMARY")
    print("=" * 50)
    
    if base_test and email_test:
        print("🎉 All tests passed!")
        print("✅ Enhanced email template is ready")
        print("🌟 Professional CVBioLabs branding applied")
        print("\n💡 Next steps:")
        print("   1. Start your Flask app: python start_app.py")
        print("   2. Test forgot password functionality")
        print("   3. Check your email for the enhanced template")
    else:
        print("⚠️  Some tests failed")
        print("💡 Please check the error messages above")
    
    return base_test and email_test

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
