# CVBioLabs Environment Configuration Template
# Copy this file to .env and update with your actual values

# Flask Application Settings
SECRET_KEY=your-very-strong-secret-key-here-change-this-in-production
FLASK_ENV=production
FLASK_DEBUG=False
PORT=7000

# Redis Configuration (for session storage)
REDIS_URL=redis://localhost:6379

# MySQL Database Configuration
DB_HOST=localhost
DB_USER=root
DB_PASSWORD=your_mysql_password_here
DB_NAME=cvbiolabs
DB_CHARSET=utf8mb4

# Email Configuration (Gmail SMTP example)
MAIL_SERVER=smtp.gmail.com
MAIL_PORT=587
MAIL_USE_TLS=True
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your_gmail_app_password_here
MAIL_DEFAULT_SENDER=<EMAIL>
MAIL_SUPPRESS_SEND=False

# Admin Default Credentials (CHANGE IN PRODUCTION!)
ADMIN_USERNAME=<EMAIL>
ADMIN_PASSWORD=your-secure-admin-password-here

# Payment Gateway Configuration (Razorpay)
# Get these from https://dashboard.razorpay.com/
RAZORPAY_KEY_ID=rzp_test_your_key_id_here
RAZORPAY_KEY_SECRET=your_razorpay_secret_here

# reCAPTCHA v3 Configuration (Google)
# Get these from https://www.google.com/recaptcha/admin
# For development, you can use Google's test keys:
RECAPTCHA_SITE_KEY=6LeIxAcTAAAAAJcZVRqyHh71UMIEGNQ_MXjiZKhI
RECAPTCHA_SECRET_KEY=6LeIxAcTAAAAAGG-vFI1TnRWxMZNFuojJ4WifJWe

# Session Configuration
SESSION_COOKIE_SECURE=False
SESSION_COOKIE_HTTPONLY=True
SESSION_COOKIE_SAMESITE=Lax
PERMANENT_SESSION_LIFETIME=86400

# File Upload Configuration
MAX_CONTENT_LENGTH=16777216
UPLOAD_FOLDER=uploads

# Security Configuration
CSRF_ENABLED=True
CSRF_SESSION_KEY=your-csrf-session-key-here

# CORS Configuration (comma-separated list of allowed origins, or * for all)
CORS_ORIGINS=http://localhost:7000,http://127.0.0.1:7000

# Application URLs (for production)
# BASE_URL=https://yourdomain.com
# API_URL=https://yourdomain.com/api

# Additional Security Configuration (REQUIRED FOR PRODUCTION)
# JWT Configuration
JWT_SECRET_KEY=your-jwt-secret-key-minimum-32-characters

# OTP Configuration
OTP_SECRET_KEY=your-base32-otp-secret-key

# Digital Signature Configuration
SIGNATURE_KEY=your-base64-encoded-signature-key

# Security Monitoring
SECURITY_ALERT_EMAIL=<EMAIL>

# Rate Limiting
RATELIMIT_STORAGE_URL=memory://

# Logging Configuration
LOG_LEVEL=INFO
SECURITY_LOG_LEVEL=WARNING

# Production Security Settings
# Uncomment and configure for production:
# SESSION_COOKIE_SECURE=True
# CORS_ORIGINS=https://yourdomain.com,https://www.yourdomain.com
# HSTS_MAX_AGE=31536000
