# CVBioLabs Application Troubleshooting Guide

## Common Issues and Solutions

### 1. Rate Limiting Issues

**Problem**: Getting "429 Too Many Requests" errors
```
flask-limiter ratelimit 5 per 1 minute (127.0.0.1) exceeded at endpoint: login
```

**Solutions**:

#### For Development:
```bash
# Clear rate limits
python clear_rate_limits.py

# Check rate limit status
curl http://localhost:7000/api/rate-limit-status

# Monitor application
python monitor_app.py
```

#### For Production:
- Increase rate limits in `rate_limit_config.py`
- Implement IP whitelisting for trusted sources
- Use Redis for distributed rate limiting
- Set up proper load balancing

### 2. SSL/TLS Handshake Errors

**Problem**: Seeing malformed HTTP requests in logs
```
Bad request version ('\x02h2\x08http/1.1\x00\x05\x00\x05\x01\x00\x00\x00\x00\x00')
```

**Cause**: SSL handshake attempts on HTTP port

**Solutions**:
- Set up proper HTTPS with SSL termination
- Use a reverse proxy (nginx) to handle SSL
- Filter these requests at the network level
- These errors are now handled gracefully and won't spam logs

### 3. Application Health Monitoring

**Health Check Endpoint**:
```bash
curl http://localhost:7000/health
```

**Response**:
```json
{
  "status": "healthy",
  "timestamp": "2025-06-23T23:15:00",
  "version": "1.0.0",
  "environment": "development",
  "components": {
    "database": "healthy",
    "redis": "not_configured",
    "rate_limiter": "healthy"
  }
}
```

### 4. Rate Limiting Configuration

Current rate limits (can be adjusted in `rate_limit_config.py`):

#### Development:
- Login: 15 per minute
- Signup: 8 per minute
- Default: 1000 per day, 200 per hour

#### Production:
- Login: 8 per minute
- Signup: 3 per minute
- Default: 500 per day, 100 per hour

### 5. Monitoring Tools

#### Application Monitor
```bash
python monitor_app.py
```
- Analyzes recent logs
- Identifies patterns in errors
- Provides recommendations

#### Rate Limit Cleaner
```bash
python clear_rate_limits.py
```
- Clears all rate limits (development only)
- Useful for testing

#### Rate Limit Configuration
```bash
python rate_limit_config.py
```
- Shows current rate limiting configuration
- Environment-specific settings

### 6. Security Improvements Made

1. **Better Rate Limiting**:
   - Environment-specific limits
   - Proper IP detection with proxy support
   - Redis support for production

2. **Enhanced Error Handling**:
   - SSL handshake attempts handled gracefully
   - Malformed requests filtered
   - Better logging and monitoring

3. **Security Headers**:
   - Comprehensive CSP policy
   - HSTS for HTTPS
   - XSS protection

4. **Monitoring**:
   - Health check endpoint
   - Security event logging
   - Rate limit monitoring

### 7. Environment Variables

Make sure these are set in your `.env` file:

```env
# Flask Environment
FLASK_ENV=development  # or production

# Rate Limiting
REDIS_URL=redis://localhost:6379/0  # Optional, for production

# Security
SECRET_KEY=your-secret-key
ADMIN_USERNAME=<EMAIL>
ADMIN_PASSWORD=secure-password

# reCAPTCHA
RECAPTCHA_SITE_KEY=your-site-key
RECAPTCHA_SECRET_KEY=your-secret-key
```

### 8. Quick Fixes

#### If you're locked out due to rate limiting:
```bash
# Method 1: Clear rate limits
python clear_rate_limits.py

# Method 2: Restart the application
# (if using memory storage)

# Method 3: Wait for the rate limit window to expire
# (1 minute for login, varies by endpoint)
```

#### If you see SSL errors:
- These are normal and handled automatically
- Consider setting up proper HTTPS in production
- Use a reverse proxy like nginx

#### If database connection fails:
```bash
# Check health endpoint
curl http://localhost:7000/health

# Check database configuration in .env
# Verify MySQL is running
```

### 9. Production Deployment Recommendations

1. **Use HTTPS**: Set up SSL certificates
2. **Reverse Proxy**: Use nginx or similar
3. **Redis**: For distributed rate limiting
4. **Monitoring**: Set up proper log aggregation
5. **Security**: Regular security audits
6. **Backup**: Database and configuration backups

### 10. Getting Help

If you continue to experience issues:

1. Check the health endpoint: `/health`
2. Run the monitoring script: `python monitor_app.py`
3. Check application logs
4. Verify environment configuration
5. Test with rate limit cleaner in development

For security-related issues, check the security logs and monitoring output.
