#!/usr/bin/env python3
"""
Test Gmail compatibility for the enhanced email template
"""

import os

def check_gmail_compatibility():
    """Check email template for Gmail compatibility"""
    print("📧 Gmail Compatibility Check")
    print("=" * 35)
    
    template_path = "templates/emails/base.html"
    
    if not os.path.exists(template_path):
        print("❌ Base template not found")
        return False
    
    try:
        with open(template_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Gmail compatibility checks
        checks = [
            ("No external images", "static/images" not in content),
            ("Uses inline styles", "style=" in content),
            ("Uses web-safe fonts", "Arial" in content or "sans-serif" in content),
            ("No JavaScript", "<script" not in content.lower()),
            ("No external CSS", "link rel=" not in content.lower()),
            ("Uses table layout", "table" in content.lower() or "div" in content.lower()),
            ("Professional text logo", "CVBioLabs" in content),
            ("Medical symbols", "⚕️" in content or "⚗️" in content),
            ("Brand colors", "#f47c20" in content and "#002f6c" in content),
            ("Responsive design", "@media" in content)
        ]
        
        passed = 0
        total = len(checks)
        
        print("📋 Compatibility Checks:")
        for check_name, result in checks:
            status = "✅" if result else "❌"
            print(f"   {status} {check_name}")
            if result:
                passed += 1
        
        print(f"\n📊 Results: {passed}/{total} checks passed")
        
        # Gmail-specific recommendations
        print(f"\n💡 Gmail Optimization:")
        if "static/images" not in content:
            print("   ✅ No external images (Gmail-friendly)")
        else:
            print("   ⚠️  External images may be blocked")
        
        if "Arial" in content:
            print("   ✅ Uses web-safe fonts")
        
        if "CVBioLabs" in content:
            print("   ✅ Professional text logo present")
        
        return passed >= (total * 0.8)  # 80% pass rate
        
    except Exception as e:
        print(f"❌ Error reading template: {e}")
        return False

def show_gmail_tips():
    """Show Gmail optimization tips"""
    print("\n📧 Gmail Email Best Practices")
    print("=" * 35)
    
    tips = [
        "✅ Use inline CSS styles (external CSS is stripped)",
        "✅ Use web-safe fonts (Arial, Helvetica, sans-serif)",
        "✅ Avoid external images (use text logos or base64)",
        "✅ Keep email width under 600px",
        "✅ Use table-based layouts for better compatibility",
        "✅ Test with Gmail's image blocking enabled",
        "✅ Use alt text for all images",
        "✅ Avoid JavaScript and external scripts",
        "✅ Use professional text-based branding",
        "✅ Include fallbacks for all visual elements"
    ]
    
    for tip in tips:
        print(f"  {tip}")

def create_gmail_test_email():
    """Create a test email specifically for Gmail"""
    print("\n🧪 Creating Gmail Test Email")
    print("-" * 30)
    
    gmail_test_html = '''
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>CVBioLabs Password Reset - Gmail Test</title>
</head>
<body style="margin: 0; padding: 20px; background-color: #f8f9fa; font-family: Arial, sans-serif;">
    <div style="max-width: 600px; margin: 0 auto; background-color: #ffffff; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
        
        <!-- Header with Text Logo -->
        <div style="background: linear-gradient(135deg, #002f6c 0%, #f47c20 100%); padding: 30px 20px; text-align: center;">
            <!-- Logo Circle -->
            <div style="display: inline-block; width: 80px; height: 80px; background: rgba(255,255,255,0.2); border-radius: 40px; line-height: 80px; text-align: center; margin-bottom: 15px; border: 3px solid rgba(255,255,255,0.3);">
                <span style="color: #ffffff; font-size: 28px; font-weight: bold; font-family: Arial, sans-serif;">CV</span>
            </div>
            
            <!-- Company Name -->
            <div style="margin-bottom: 10px;">
                <span style="color: #ffffff; font-size: 36px; font-weight: bold; letter-spacing: 4px; font-family: Arial, sans-serif;">
                    <span style="color: #ffffff;">CV</span><span style="color: #e6f7ff;">BioLabs</span>
                </span>
            </div>
            
            <!-- Medical Symbol -->
            <div style="margin: 15px 0;">
                <span style="color: #e6f7ff; font-size: 24px;">⚕️</span>
            </div>
            
            <!-- Tagline -->
            <div style="color: #e6f7ff; font-size: 16px; font-weight: 400; font-family: Arial, sans-serif;">
                For a Healthy Life - Advanced Medical Diagnostics
            </div>
        </div>
        
        <!-- Content -->
        <div style="padding: 40px 30px; font-family: Arial, sans-serif;">
            <div style="font-size: 18px; color: #002f6c; margin-bottom: 20px; font-weight: 600;">
                Dear Valued Customer,
            </div>
            
            <div style="font-size: 16px; line-height: 1.6; color: #333333; margin-bottom: 20px;">
                This is a test email to verify Gmail compatibility. Your CVBioLabs branding should display correctly even with images blocked.
            </div>
            
            <!-- Test OTP Display -->
            <div style="text-align: center; margin: 30px 0;">
                <div style="background: linear-gradient(135deg, #f47c20 0%, #ff8c42 100%); border-radius: 15px; padding: 25px; display: inline-block;">
                    <div style="color: #ffffff; font-size: 16px; margin-bottom: 15px; font-weight: 600;">
                        🔐 Test Code
                    </div>
                    <div style="background: #ffffff; border-radius: 10px; padding: 20px;">
                        <div style="font-size: 36px; font-weight: bold; color: #002f6c; letter-spacing: 8px; font-family: monospace;">
                            123456
                        </div>
                    </div>
                </div>
            </div>
            
            <div style="text-align: center; margin: 30px 0;">
                <a href="#" style="display: inline-block; background: linear-gradient(135deg, #002f6c 0%, #004080 100%); color: #ffffff; padding: 15px 30px; text-decoration: none; border-radius: 8px; font-weight: 600; font-family: Arial, sans-serif;">
                    Test Button
                </a>
            </div>
        </div>
        
        <!-- Footer -->
        <div style="background-color: #002f6c; color: #ffffff; padding: 30px 20px; text-align: center; font-family: Arial, sans-serif;">
            <div style="font-size: 14px;">
                <strong>CVBioLabs</strong><br>
                Advanced Medical Diagnostics<br>
                <EMAIL>
            </div>
        </div>
    </div>
</body>
</html>
'''
    
    # Save test email
    with open("gmail_test_email.html", "w", encoding="utf-8") as f:
        f.write(gmail_test_html)
    
    print("✅ Gmail test email created: gmail_test_email.html")
    print("💡 Open this file in a browser to preview")
    print("📧 Send this as a test email to check Gmail rendering")

def main():
    """Main function"""
    print("📧 CVBioLabs Gmail Compatibility Test")
    print("=" * 40)
    
    # Check compatibility
    compatible = check_gmail_compatibility()
    
    # Show tips
    show_gmail_tips()
    
    # Create test email
    create_gmail_test_email()
    
    # Summary
    print("\n" + "=" * 40)
    print("📊 SUMMARY")
    print("=" * 40)
    
    if compatible:
        print("✅ Email template is Gmail-compatible!")
        print("🎯 Key improvements for Gmail:")
        print("   • Text-based logo (no external images)")
        print("   • Professional CVBioLabs branding")
        print("   • Web-safe fonts and inline styles")
        print("   • Medical symbols for visual appeal")
        print("   • Responsive design")
    else:
        print("⚠️  Some compatibility issues found")
        print("💡 Review the checks above for improvements")
    
    print(f"\n🧪 Next Steps:")
    print(f"   1. Test with: gmail_test_email.html")
    print(f"   2. Send test email to your Gmail")
    print(f"   3. Check rendering with images blocked")
    print(f"   4. Verify branding appears correctly")
    
    return compatible

if __name__ == '__main__':
    success = main()
    exit(0 if success else 1)
