-- Update sample_collections table to include 'Done' status
-- This script adds 'Done' to the collection_status ENUM

USE cvbiolabs;

-- First, let's check the current structure
DESCRIBE sample_collections;

-- Alter the table to modify the ENUM to include 'Done'
-- We need to use ALTER TABLE ... <PERSON>AN<PERSON> to modify an ENUM column
ALTER TABLE sample_collections
CHANGE COLUMN collection_status collection_status ENUM('Pending', 'Collected', 'Delivered', 'Done') NOT NULL DEFAULT 'Pending';

-- Verify the change
DESCRIBE sample_collections;
